<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <!-- 3次装填消防泡沫包 -->
  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_FirefoampopPackEnhanced</defName>
    <label>firefoam pop pack (3 charges)</label>
    <description>A utility pack made for firefighters with enhanced capacity. When triggered, a burst of fire-retardant foam is released, covering a wide circle around its wearer. This version can be refilled three times.</description>
    <techLevel>Industrial</techLevel>
    
    <!-- 使用原版贴图 -->
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/FirefoamPack/FirefoamPack</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    
    <!-- 使用原版组件，只修改装填次数 -->
    <comps>
      <li Class="CompProperties_ApparelReloadable">
        <maxCharges>3</maxCharges>
        <ammoDef>Chemfuel</ammoDef>
        <ammoCountToRefill>90</ammoCountToRefill>
        <baseReloadTicks>60</baseReloadTicks>
        <soundReload>Standard_Reload</soundReload>
        <hotKey>Misc4</hotKey>
        <chargeNoun>firefoam pop</chargeNoun>
        <displayGizmoWhileUndrafted>true</displayGizmoWhileUndrafted>
      </li>
      <li Class="CompProperties_Explosive">
        <explosiveRadius>4.9</explosiveRadius>
        <explosiveDamageType>Extinguish</explosiveDamageType>
        <startWickHitPointsPercent>0</startWickHitPointsPercent>
        <chanceNeverExplodeFromDamage>1</chanceNeverExplodeFromDamage>
        <postExplosionSpawnThingDef>Filth_FireFoam</postExplosionSpawnThingDef>
        <postExplosionSpawnChance>1</postExplosionSpawnChance>
        <postExplosionSpawnThingCount>3</postExplosionSpawnThingCount>
        <applyDamageToExplosionCellsNeighbors>true</applyDamageToExplosionCellsNeighbors>
        <explosionEffect>ExtinguisherExplosion</explosionEffect>
        <explosionSound>Explosion_FirefoamPopPack</explosionSound>
        <wickTicks>0</wickTicks>
        <destroyThingOnExplosionSize>9999</destroyThingOnExplosionSize>
      </li>
      <li Class="CompProperties_AIUSablePack">
        <compClass>CompFirefoamPack</compClass>
        <checkInterval>60</checkInterval>
      </li>
    </comps>
    
    <!-- 使用原版动词 -->
    <verbs>
      <li>
        <verbClass>Verb_FirefoamPop</verbClass>
        <label>pop firefoam</label>
        <violent>false</violent>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>false</targetable>
        <nonInterruptingSelfCast>true</nonInterruptingSelfCast>
        <onlyManualCast>True</onlyManualCast>
      </li>
    </verbs>
    
    <!-- 制作配方 -->
    <recipeMaker>
      <unfinishedThingDef>UnfinishedPack</unfinishedThingDef>
      <researchPrerequisite>FirefoamEnhanced</researchPrerequisite>
      <recipeUsers>
        <li>TableMachining</li>
      </recipeUsers>
      <useIngredientsForColor>false</useIngredientsForColor>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Smith</soundWorking>
      <skillRequirements>
        <Crafting>3</Crafting>
      </skillRequirements>
      <displayPriority>321</displayPriority>
    </recipeMaker>
    
    <!-- 制作材料：原版的3倍 -->
    <costList>
      <ComponentIndustrial>3</ComponentIndustrial>
      <Steel>60</Steel>
      <Chemfuel>90</Chemfuel>
    </costList>
    
    <tickerType>Normal</tickerType>
    
    <!-- 基础属性 -->
    <statBases>
      <WorkToMake>4800</WorkToMake>
      <Mass>4</Mass>
      <Flammability>0.4</Flammability>
      <PackRadius>4.9</PackRadius>
      <EquipDelay>2</EquipDelay>
    </statBases>
    
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    
    <generateAllowChance>0.15</generateAllowChance>
    
    <!-- 装备属性 -->
    <apparel>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/FirefoamPack/FirefoamPack</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <offset>(0,-0.15)</offset>
        </north>
        <south>
          <offset>(0,0.1)</offset>
          <scale>(1.3,1)</scale>
        </south>
        <east>
          <offset>(-0.35,-0.05)</offset>
          <thin>
            <offset>(0.05,0)</offset>
          </thin>
          <hulk>
            <offset>(-0.1,0)</offset>
          </hulk>
          <fat>
            <offset>(-0.15,0)</offset>
          </fat>
        </east>
        <west>
          <offset>(0.35,-0.05)</offset>
          <thin>
            <offset>(-0.05,0)</offset>
          </thin>
          <hulk>
            <offset>(0.1,0)</offset>
          </hulk>
          <fat>
            <offset>(0.15,0)</offset>
          </fat>
        </west>
        <male>
          <scale>(0.8,0.8)</scale>
        </male>
        <female>
          <scale>(0.75,0.75)</scale>
        </female>
        <thin>
          <scale>(0.7,0.7)</scale>
        </thin>
        <hulk>
          <scale>(1.2,1.2)</scale>
        </hulk>
        <fat>
          <scale>(1.1,1.1)</scale>
        </fat>
      </wornGraphicData>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <tags>
        <li>BeltDefensePop</li>
      </tags>
    </apparel>
    
    <allowedArchonexusCount>1</allowedArchonexusCount>
    
    <tradeTags>
      <li>Clothing</li>
    </tradeTags>
  </ThingDef>

  <!-- 5次装填消防泡沫包 -->
  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_FirefoampopPackAdvanced</defName>
    <label>firefoam pop pack (5 charges)</label>
    <description>A utility pack made for firefighters with maximum capacity. When triggered, a burst of fire-retardant foam is released, covering a wide circle around its wearer. This version can be refilled five times.</description>
    <techLevel>Industrial</techLevel>
    
    <!-- 使用原版贴图 -->
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/FirefoamPack/FirefoamPack</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    
    <!-- 使用原版组件，只修改装填次数 -->
    <comps>
      <li Class="CompProperties_ApparelReloadable">
        <maxCharges>5</maxCharges>
        <ammoDef>Chemfuel</ammoDef>
        <ammoCountToRefill>150</ammoCountToRefill>
        <baseReloadTicks>60</baseReloadTicks>
        <soundReload>Standard_Reload</soundReload>
        <hotKey>Misc4</hotKey>
        <chargeNoun>firefoam pop</chargeNoun>
        <displayGizmoWhileUndrafted>true</displayGizmoWhileUndrafted>
      </li>
      <li Class="CompProperties_Explosive">
        <explosiveRadius>4.9</explosiveRadius>
        <explosiveDamageType>Extinguish</explosiveDamageType>
        <startWickHitPointsPercent>0</startWickHitPointsPercent>
        <chanceNeverExplodeFromDamage>1</chanceNeverExplodeFromDamage>
        <postExplosionSpawnThingDef>Filth_FireFoam</postExplosionSpawnThingDef>
        <postExplosionSpawnChance>1</postExplosionSpawnChance>
        <postExplosionSpawnThingCount>3</postExplosionSpawnThingCount>
        <applyDamageToExplosionCellsNeighbors>true</applyDamageToExplosionCellsNeighbors>
        <explosionEffect>ExtinguisherExplosion</explosionEffect>
        <explosionSound>Explosion_FirefoamPopPack</explosionSound>
        <wickTicks>0</wickTicks>
        <destroyThingOnExplosionSize>9999</destroyThingOnExplosionSize>
      </li>
      <li Class="CompProperties_AIUSablePack">
        <compClass>CompFirefoamPack</compClass>
        <checkInterval>60</checkInterval>
      </li>
    </comps>
    
    <!-- 使用原版动词 -->
    <verbs>
      <li>
        <verbClass>Verb_FirefoamPop</verbClass>
        <label>pop firefoam</label>
        <violent>false</violent>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>false</targetable>
        <nonInterruptingSelfCast>true</nonInterruptingSelfCast>
        <onlyManualCast>True</onlyManualCast>
      </li>
    </verbs>
    
    <!-- 制作配方 -->
    <recipeMaker>
      <unfinishedThingDef>UnfinishedPack</unfinishedThingDef>
      <researchPrerequisite>FirefoamEnhanced</researchPrerequisite>
      <recipeUsers>
        <li>TableMachining</li>
      </recipeUsers>
      <useIngredientsForColor>false</useIngredientsForColor>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Smith</soundWorking>
      <skillRequirements>
        <Crafting>3</Crafting>
      </skillRequirements>
      <displayPriority>322</displayPriority>
    </recipeMaker>
    
    <!-- 制作材料：原版的5倍 -->
    <costList>
      <ComponentIndustrial>5</ComponentIndustrial>
      <Steel>100</Steel>
      <Chemfuel>150</Chemfuel>
    </costList>
    
    <tickerType>Normal</tickerType>
    
    <!-- 基础属性 -->
    <statBases>
      <WorkToMake>6000</WorkToMake>
      <Mass>5</Mass>
      <Flammability>0.4</Flammability>
      <PackRadius>4.9</PackRadius>
      <EquipDelay>2</EquipDelay>
    </statBases>
    
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    
    <generateAllowChance>0.15</generateAllowChance>
    
    <!-- 装备属性 -->
    <apparel>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/FirefoamPack/FirefoamPack</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <offset>(0,-0.15)</offset>
        </north>
        <south>
          <offset>(0,0.1)</offset>
          <scale>(1.3,1)</scale>
        </south>
        <east>
          <offset>(-0.35,-0.05)</offset>
          <thin>
            <offset>(0.05,0)</offset>
          </thin>
          <hulk>
            <offset>(-0.1,0)</offset>
          </hulk>
          <fat>
            <offset>(-0.15,0)</offset>
          </fat>
        </east>
        <west>
          <offset>(0.35,-0.05)</offset>
          <thin>
            <offset>(-0.05,0)</offset>
          </thin>
          <hulk>
            <offset>(0.1,0)</offset>
          </hulk>
          <fat>
            <offset>(0.15,0)</offset>
          </fat>
        </west>
        <male>
          <scale>(0.8,0.8)</scale>
        </male>
        <female>
          <scale>(0.75,0.75)</scale>
        </female>
        <thin>
          <scale>(0.7,0.7)</scale>
        </thin>
        <hulk>
          <scale>(1.2,1.2)</scale>
        </hulk>
        <fat>
          <scale>(1.1,1.1)</scale>
        </fat>
      </wornGraphicData>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <tags>
        <li>BeltDefensePop</li>
      </tags>
    </apparel>
    
    <allowedArchonexusCount>1</allowedArchonexusCount>
    
    <tradeTags>
      <li>Clothing</li>
    </tradeTags>
  </ThingDef>
</Defs> 